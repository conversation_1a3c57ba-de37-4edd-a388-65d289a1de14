<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualizador de Geolocalização</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #f4f7f6;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        #container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px 15px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #4CAF50;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #ddd;
        }
        #status {
            text-align: center;
            margin-top: 15px;
            font-style: italic;
            color: #777;
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-radius: 50%;
            border-top: 4px solid #3498db;
            width: 20px;
            height: 20px;
            animation: spin 2s linear infinite;
            display: inline-block;
            vertical-align: middle;
            margin-left: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>

    <div id="container">
        <h1><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="vertical-align: -4px; margin-right: 8px;"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>Dispositivos Online</h1>
        <div id="data-table">
            <table>
                <thead>
                    <tr>
                        <th>Apelido</th>
                        <th>ID do Dispositivo</th>
                        <th>Latitude</th>
                        <th>Longitude</th>
                        <th>Timestamp (Dispositivo)</th>
                        <th>Recebido em (Servidor)</th>
                    </tr>
                </thead>
                <tbody id="devices-tbody">
                    <!-- Os dados serão inseridos aqui via JavaScript -->
                </tbody>
            </table>
        </div>
        <div id="status">
            Carregando dados... <span id="spinner" class="loader"></span>
        </div>
    </div>

    <script>
        const API_URL = 'geotest.php';
        const UPDATE_INTERVAL = 30000; // 30 segundos

        const devicesTbody = document.getElementById('devices-tbody');
        const statusDiv = document.getElementById('status');
        const spinner = document.getElementById('spinner');

        async function fetchDevices() {
            try {
                spinner.style.display = 'inline-block';
                const response = await fetch(API_URL);
                if (!response.ok) {
                    throw new Error(`Erro na rede: ${response.statusText}`);
                }
                const data = await response.json();

                if (data.success && Array.isArray(data.devices)) {
                    updateTable(data.devices);
                } else {
                    throw new Error('Formato da resposta inválido.');
                }
                
                statusDiv.textContent = `Atualizado em: ${new Date().toLocaleTimeString()}`;

            } catch (error) {
                console.error('Falha ao buscar dados:', error);
                statusDiv.innerHTML = `Falha ao carregar. Tentando novamente em ${UPDATE_INTERVAL / 1000}s.`;
            } finally {
                 if (spinner) spinner.style.display = 'none';
            }
        }

        function updateTable(devices) {
            // Limpa o corpo da tabela
            devicesTbody.innerHTML = '';

            if (devices.length === 0) {
                const row = devicesTbody.insertRow();
                const cell = row.insertCell();
                cell.colSpan = 6;
                cell.textContent = 'Nenhum dispositivo online no momento.';
                cell.style.textAlign = 'center';
            } else {
                devices.forEach(device => {
                    const row = devicesTbody.insertRow();
                    row.insertCell().textContent = device.nickname || 'Sem apelido';
                    row.insertCell().textContent = device.device_id || 'N/A';
                    row.insertCell().textContent = device.latitude || 'N/A';
                    row.insertCell().textContent = device.longitude || 'N/A';
                    row.insertCell().textContent = device.timestamp ? new Date(device.timestamp).toLocaleString('pt-BR') : 'N/A';
                    row.insertCell().textContent = device.received_at || 'N/A';
                });
            }
        }

        // Carregar imediatamente e depois a cada 30 segundos
        document.addEventListener('DOMContentLoaded', () => {
            fetchDevices();
            setInterval(fetchDevices, UPDATE_INTERVAL);
        });
    </script>

</body>
</html> 