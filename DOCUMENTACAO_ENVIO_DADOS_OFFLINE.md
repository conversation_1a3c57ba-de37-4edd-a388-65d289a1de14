# Documentação: Sistema de Envio de Dados de Localização - GeoSec App

## Visão Geral

O GeoSec App é um aplicativo Flutter que rastreia a localização GPS do usuário e a envia para um servidor em tempo real. **Importante**: O aplicativo não possui um sistema de armazenamento offline real, mas sim um sistema de envio periódico que funciona em background.

## Arquitetura do Sistema

### Componentes Principais

1. **LocationService** (`lib/services/location_service.dart`)
2. **HomeScreen** (`lib/screens/home_screen.dart`)
3. **FlutterBackgroundService** (serviço em background)
4. **Backend PHP** (`geotest.php`)

## Como Funciona o "Envio Offline"

### 1. Armazenamento Local de Configurações

O aplicativo utiliza `SharedPreferences` para armazenar:

```dart
static const String _isOnlineKey = 'is_online';
static const String _deviceIdKey = 'device_id';
static const String _nicknameKey = 'device_nickname';
```

**Dados armazenados localmente:**

- Status online/offline do dispositivo
- ID único do dispositivo (gerado automaticamente)
- Nickname do usuário

### 2. Geração de ID do Dispositivo

```dart
static Future<void> initDeviceId() async {
  // Verifica se já existe um ID salvo
  String? savedId = prefs.getString(_deviceIdKey);

  if (savedId != null) {
    _cachedDeviceId = savedId;
    return;
  }

  // Gera ID baseado na plataforma
  if (Platform.isAndroid) {
    deviceId = 'android_${androidInfo.id}';
  } else if (Platform.isIOS) {
    deviceId = 'ios_${iosInfo.identifierForVendor}';
  }
}
```

### 3. Sistema de Envio Periódico

#### Configuração do Timer

```dart
static const int _updateInterval = 20000; // 20 segundos

static Future<void> startPeriodicLocationUpdates() async {
  _locationTimer = Timer.periodic(Duration(milliseconds: _updateInterval), (timer) async {
    try {
      Position? position = await getCurrentLocation();
      if (position != null) {
        String deviceId = await getDeviceId();
        await sendLocation(deviceId, position.latitude, position.longitude);
      }
    } catch (e) {
      print('Erro no envio periódico de localização: $e');
    }
  });
}
```

#### Processo de Envio

1. **Obtenção da localização atual**
2. **Recuperação do Device ID e Nickname**
3. **Tentativa de envio para o servidor**
4. **Tratamento de erros (sem retry automático)**

### 4. Serviço em Background

#### Configuração Android

```xml
<!-- AndroidManifest.xml -->
<service
    android:name="id.flutter.flutter_background_service.BackgroundService"
    android:foregroundServiceType="location"
    android:exported="true" />
```

#### Inicialização do Serviço

```dart
void _toggleOnlineStatus() async {
  final service = FlutterBackgroundService();

  if (!isRunning) {
    await service.startService();
    await LocationService.setOnlineStatus(true);
    await LocationService.startPeriodicLocationUpdates();
  }
}
```

## Limitações do Sistema Atual

### ❌ O que NÃO existe:

1. **Armazenamento offline de dados de localização**

   - Não há queue local para dados não enviados
   - Não há sincronização quando a conexão é restaurada
   - Dados perdidos em caso de falha de rede são descartados

2. **Sistema de retry automático**

   - Se o envio falhar, o dado é perdido
   - Não há tentativas de reenvio

3. **Cache de dados**
   - Não há armazenamento local de histórico de localizações
   - Não há backup local dos dados

### ✅ O que existe:

1. **Envio periódico em background**

   - Timer que executa a cada 20 segundos
   - Funciona mesmo com o app em segundo plano

2. **Persistência de configurações**

   - Device ID salvo localmente
   - Nickname do usuário
   - Status online/offline

3. **Tratamento básico de erros**
   - Logs de erro são gerados
   - Aplicativo continua funcionando mesmo com falhas

## Fluxo de Dados

### 1. Inicialização

```
App Start → Load Device ID → Load Nickname → Check Background Service Status
```

### 2. Modo Online

```
User Toggle Online → Start Background Service → Start Periodic Timer →
Every 20s: Get Location → Send to Server → Update UI
```

### 3. Envio de Dados

```
Get Current Position → Get Device ID → Get Nickname →
Create JSON Payload → HTTP POST to Server → Handle Response
```

### 4. Estrutura do Payload

```json
{
  "device_id": "android_abc123",
  "latitude": -23.5505,
  "longitude": -46.6333,
  "timestamp": "2024-01-01T12:00:00.000Z",
  "nickname": "Usuario1"
}
```

## Backend (PHP)

### Funcionalidades do Servidor

1. **Limpeza automática de dados antigos** (> 1 minuto)
2. **Armazenamento em arquivo texto** (`geolocations.txt`)
3. **Atualização por nickname** (evita duplicatas)
4. **Retorno de todos os dispositivos ativos**

### Endpoints

- **POST**: Recebe nova localização
- **PUT**: Atualiza nickname
- **GET**: Retorna todos os dispositivos

## Dependências Utilizadas

```yaml
dependencies:
  geolocator: ^10.1.0 # GPS/Localização
  permission_handler: ^11.0.1 # Permissões
  http: ^1.1.2 # Requisições HTTP
  shared_preferences: ^2.2.2 # Armazenamento local
  flutter_background_service: ^5.1.0 # Serviço em background
  device_info_plus: ^9.1.1 # Informações do dispositivo
  flutter_local_notifications: ^17.0.0 # Notificações
```

## Melhorias Sugeridas para um Sistema Offline Real

### 1. Implementar Queue Local

```dart
// Exemplo de estrutura para queue offline
class OfflineLocationQueue {
  static const String _queueKey = 'location_queue';

  static Future<void> addToQueue(LocationData data) async {
    // Adicionar à queue local
  }

  static Future<void> syncQueue() async {
    // Sincronizar dados quando online
  }
}
```

### 2. Detector de Conectividade

```dart
// Monitorar status da conexão
import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityService {
  static Future<bool> isOnline() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }
}
```

### 3. Sistema de Retry

```dart
// Implementar retry com backoff exponencial
class RetryService {
  static Future<bool> sendWithRetry(LocationData data, {int maxRetries = 3}) async {
    // Lógica de retry
  }
}
```

## Detalhes Técnicos de Implementação

### Método sendLocation - Análise Detalhada

```dart
static Future<List<DeviceData>?> sendLocation(String deviceId, double latitude, double longitude) async {
  try {
    String? nickname = await getNickname();

    final response = await http.post(
      Uri.parse(_apiUrl),
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'device_id': deviceId,
        'latitude': latitude,
        'longitude': longitude,
        'timestamp': DateTime.now().toIso8601String(),
        'nickname': nickname,
      }),
    );

    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['success'] == true && data['devices'] != null) {
        List<DeviceData> devices = [];
        for (var deviceJson in data['devices']) {
          devices.add(DeviceData.fromJson(deviceJson));
        }
        return devices;
      }
    }
    return null; // ❌ Dados perdidos em caso de falha
  } catch (e) {
    print('Erro ao enviar localização: $e');
    return null; // ❌ Dados perdidos em caso de exceção
  }
}
```

**Problemas identificados:**

- Não há tratamento para falhas de rede
- Dados são descartados se o envio falhar
- Não há queue para reenvio posterior

### Gerenciamento de Estado Online/Offline

```dart
// Armazenamento do status
static Future<void> setOnlineStatus(bool isOnline) async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  await prefs.setBool(_isOnlineKey, isOnline);
}

// Recuperação do status
static Future<bool> getOnlineStatus() async {
  SharedPreferences prefs = await SharedPreferences.getInstance();
  return prefs.getBool(_isOnlineKey) ?? false;
}
```

### Comunicação entre Background Service e UI

```dart
// No HomeScreen - Escuta eventos do background service
service.on('update').listen((event) {
  if(event == null) return;

  final devicesData = event['devices'];
  if(devicesData is List){
    setState(() {
      _allDevices = devicesData.map((d) => DeviceData.fromJson(d as Map<String, dynamic>)).toList();
    });
  }
});
```

## Análise do Backend PHP

### Limpeza Automática de Dados

```php
function cleanOldData($dataFile) {
    $lines = file($dataFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $freshData = [];
    $now = time();

    foreach ($lines as $line) {
        $data = json_decode($line, true);
        if ($data && (isset($data['received_at_unix']))) {
            if (($now - $data['received_at_unix']) <= 60) { // 60 segundos
                $freshData[] = $line;
            }
        }
    }

    file_put_contents($dataFile, implode(PHP_EOL, $freshData) . PHP_EOL);
}
```

**Características:**

- Remove dispositivos inativos há mais de 1 minuto
- Executa a cada requisição (overhead)
- Armazenamento em arquivo texto simples

### Tratamento de Duplicatas por Nickname

```php
// Atualiza dispositivo existente baseado no nickname
foreach ($lines as $line) {
    $existingData = json_decode($line, true);
    if ($existingData && isset($existingData['nickname']) &&
        $nicknameKey && $existingData['nickname'] === $nicknameKey) {

        // Verifica se algum dado mudou
        if ($existingData['latitude'] != $newData['latitude'] ||
            $existingData['longitude'] != $newData['longitude']) {
            $newLines[] = json_encode($newData);
            $updated = true;
        }
    }
}
```

## Configurações de Permissões

### Android Manifest

```xml
<!-- Permissões necessárias -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />

<!-- Configuração do serviço em background -->
<service
    android:name="id.flutter.flutter_background_service.BackgroundService"
    android:foregroundServiceType="location"
    android:exported="true" />
```

### Verificação de Permissões no Código

```dart
static Future<bool> checkPermissions() async {
  bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
  if (!serviceEnabled) return false;

  LocationPermission permission = await Geolocator.checkPermission();
  if (permission == LocationPermission.denied ||
      permission == LocationPermission.deniedForever) {
    return false;
  }

  return true;
}
```

## Monitoramento e Logs

### Sistema de Logs Atual

```dart
// Logs no cliente
print('Localização enviada periodicamente: ${position.latitude}, ${position.longitude}');
print('Erro no envio periódico de localização: $e');

// Logs no servidor PHP
error_log("POST recebido: " . json_encode($input));
error_log("Dispositivo (nickname) atualizado: " . $nicknameKey);
error_log("Total de dispositivos retornados: " . count($allDevices));
```

## Conclusão

O sistema atual do GeoSec App funciona como um **rastreador em tempo real** com envio periódico, mas **não possui capacidades offline reais**. Para um sistema verdadeiramente offline, seria necessário implementar:

1. Queue local para dados não enviados
2. Sincronização automática quando a conexão for restaurada
3. Sistema de retry com backoff
4. Monitoramento de conectividade
5. Armazenamento local robusto (SQLite)

O sistema atual é adequado para cenários onde a conectividade é estável, mas pode perder dados em situações de rede instável.

### Recomendações de Melhoria

1. **Implementar SQLite** para armazenamento local robusto
2. **Adicionar connectivity_plus** para monitorar status da rede
3. **Criar sistema de queue** com retry automático
4. **Implementar sincronização** quando a conexão for restaurada
5. **Adicionar compressão** para otimizar transferência de dados
6. **Implementar criptografia** para segurança dos dados
