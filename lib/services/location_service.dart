import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class DeviceData {
  final String deviceId;
  final double latitude;
  final double longitude;
  final String timestamp;
  final String receivedAt;
  final String? nickname;

  DeviceData({
    required this.deviceId,
    required this.latitude,
    required this.longitude,
    required this.timestamp,
    required this.receivedAt,
    this.nickname,
  });

  factory DeviceData.fromJson(Map<String, dynamic> json) {
    return DeviceData(
      deviceId: json['device_id'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      timestamp: json['timestamp'],
      receivedAt: json['received_at'],
      nickname: json['nickname'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'device_id': deviceId,
      'latitude': latitude,
      'longitude': longitude,
      'timestamp': timestamp,
      'received_at': receivedAt,
      'nickname': nickname,
    };
  }
}

class LocationService {
  static const String _apiUrl = 'https://pool.taliso.com.br/geotest.php';
  static const String _isOnlineKey = 'is_online';
  static const String _deviceIdKey = 'device_id';
  static const String _nicknameKey = 'device_nickname';
  static const int _updateInterval = 20000; // 20 segundos

  static String? _cachedDeviceId;
  static String? _cachedNickname;
  static Timer? _locationTimer;

  static Future<void> initDeviceId() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? savedId = prefs.getString(_deviceIdKey);
      
      if (savedId != null) {
        _cachedDeviceId = savedId;
        return;
      }

      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      String deviceId;

      if (Platform.isAndroid) {
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        deviceId = 'android_${androidInfo.id}';
      } else if (Platform.isIOS) {
        IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        deviceId = 'ios_${iosInfo.identifierForVendor}';
      } else {
        deviceId = 'device_${DateTime.now().millisecondsSinceEpoch}';
      }

      await prefs.setString(_deviceIdKey, deviceId);
      _cachedDeviceId = deviceId;
    } catch (e) {
      print('Erro ao inicializar ID do dispositivo: $e');
      // Não tentar salvar um fallback aqui para não chamar prefs no background
    }
  }

  static Future<bool> checkPermissions() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return false;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
      return false;
    }

    return true;
  }

  static Future<void> requestPermissions() async {
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      await Geolocator.requestPermission();
    }
  }

  static Future<Position?> getCurrentLocation() async {
    try {
      bool hasPermission = await checkPermissions();
      if (!hasPermission) {
        return null;
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      return position;
    } catch (e) {
      print('Erro ao obter localização: $e');
      return null;
    }
  }

  static Future<String> getDeviceId() async {
    if (_cachedDeviceId != null) {
      return _cachedDeviceId!;
    }

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? savedId = prefs.getString(_deviceIdKey);
      
      if (savedId != null) {
        _cachedDeviceId = savedId;
        return savedId;
      }

      // Gerar um ID único baseado no timestamp para evitar conflitos
      String fallbackId = 'device_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecondsSinceEpoch}';
      _cachedDeviceId = fallbackId;
      return fallbackId;

    } catch (e) {
      print('Erro ao obter ID do dispositivo no background: $e');
      // Gerar ID único mesmo em caso de erro
      String errorId = 'device_error_${DateTime.now().millisecondsSinceEpoch}';
      _cachedDeviceId = errorId;
      return errorId;
    }
  }

  static Future<String?> getNickname() async {
    if (_cachedNickname != null) {
      return _cachedNickname;
    }

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? nickname = prefs.getString(_nicknameKey);
      _cachedNickname = nickname;
      return nickname;
    } catch (e) {
      print('Erro ao obter nickname: $e');
      return null;
    }
  }

  static Future<void> setNickname(String nickname) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      await prefs.setString(_nicknameKey, nickname);
      _cachedNickname = nickname;
      
      // Atualiza o nickname no servidor
      String deviceId = await getDeviceId();
      await updateNicknameOnServer(deviceId, nickname);
    } catch (e) {
      print('Erro ao salvar nickname: $e');
    }
  }

  static Future<void> updateNicknameOnServer(String deviceId, String nickname) async {
    try {
      final response = await http.put(
        Uri.parse(_apiUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'device_id': deviceId,
          'nickname': nickname,
        }),
      );

      if (response.statusCode == 200) {
        print('Nickname atualizado no servidor: $nickname');
      } else {
        print('Erro ao atualizar nickname no servidor: ${response.statusCode}');
      }
    } catch (e) {
      print('Erro ao atualizar nickname no servidor: $e');
    }
  }

  static Future<List<DeviceData>?> sendLocation(String deviceId, double latitude, double longitude) async {
    try {
      String? nickname = await getNickname();
      
      final response = await http.post(
        Uri.parse(_apiUrl),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'device_id': deviceId,
          'latitude': latitude,
          'longitude': longitude,
          'timestamp': DateTime.now().toIso8601String(),
          'nickname': nickname,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['devices'] != null) {
          List<DeviceData> devices = [];
          for (var deviceJson in data['devices']) {
            devices.add(DeviceData.fromJson(deviceJson));
          }
          return devices;
        }
      }
      return null;
    } catch (e) {
      print('Erro ao enviar localização: $e');
      return null;
    }
  }

  static Future<List<DeviceData>?> getAllDevices() async {
    try {
      final response = await http.get(Uri.parse(_apiUrl));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true && data['devices'] != null) {
          List<DeviceData> devices = [];
          for (var deviceJson in data['devices']) {
            devices.add(DeviceData.fromJson(deviceJson));
          }
          return devices;
        }
      }
      return null;
    } catch (e) {
      print('Erro ao buscar dispositivos: $e');
      return null;
    }
  }

  static Future<void> setOnlineStatus(bool isOnline) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isOnlineKey, isOnline);
  }

  static Future<bool> getOnlineStatus() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isOnlineKey) ?? false;
  }

  static Future<void> startPeriodicLocationUpdates() async {
    // Para qualquer timer existente
    _locationTimer?.cancel();
    
    // Inicia novo timer para enviar localização a cada 20 segundos
    _locationTimer = Timer.periodic(Duration(milliseconds: _updateInterval), (timer) async {
      try {
        Position? position = await getCurrentLocation();
        if (position != null) {
          String deviceId = await getDeviceId();
          await sendLocation(deviceId, position.latitude, position.longitude);
          print('Localização enviada periodicamente: ${position.latitude}, ${position.longitude}');
        }
      } catch (e) {
        print('Erro no envio periódico de localização: $e');
      }
    });
    
    print('Iniciado envio periódico de localização a cada ${_updateInterval / 1000} segundos');
  }

  static void stopPeriodicLocationUpdates() {
    _locationTimer?.cancel();
    _locationTimer = null;
    print('Parado envio periódico de localização');
  }
} 