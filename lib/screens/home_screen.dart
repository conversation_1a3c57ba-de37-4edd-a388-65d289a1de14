import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/location_service.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool _isOnline = false;
  Position? _currentPosition;
  String _statusMessage = 'Aguardando...';
  List<DeviceData> _allDevices = [];
  String _deviceId = '';
  String _nickname = '';
  StreamSubscription<Position>? _positionStreamSubscription;
  Timer? _deviceUpdateTimer;
  final TextEditingController _nicknameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  Future<void> _initialize() async {
    await _loadDeviceId();
    await _loadNickname();
    await _getCurrentLocation();
    
    final service = FlutterBackgroundService();
    bool isRunning = await service.isRunning();
    
    setState(() {
      _isOnline = isRunning;
      _statusMessage = isRunning ? 'Online' : 'Offline';
    });

    service.on('update').listen((event) {
      if(event == null) return;
      
      final devicesData = event['devices'];
      if(devicesData is List){
        setState(() {
          _allDevices = devicesData.map((d) => DeviceData.fromJson(d as Map<String, dynamic>)).toList();
        });
      }
    });

     service.on('updateNotification').listen((event) {
        // A notificação já é atualizada no background, podemos usar isso para atualizar a UI se quisermos.
     });

    _deviceUpdateTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (_isOnline) {
        _loadAllDevices();
      }
    });
  }

  @override
  void dispose() {
    _positionStreamSubscription?.cancel();
    _deviceUpdateTimer?.cancel();
    _nicknameController.dispose();
    super.dispose();
  }

  Future<void> _loadDeviceId() async {
    String deviceId = await LocationService.getDeviceId();
    setState(() {
      _deviceId = deviceId;
    });
  }

  Future<void> _loadNickname() async {
    String? nickname = await LocationService.getNickname();
    setState(() {
      _nickname = nickname ?? '';
      _nicknameController.text = nickname ?? '';
    });
  }

  Future<void> _saveNickname() async {
    String newNickname = _nicknameController.text.trim();
    if (newNickname.isNotEmpty) {
      await LocationService.setNickname(newNickname);
      setState(() {
        _nickname = newNickname;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Apelido salvo: $newNickname')),
      );
    }
  }

  Future<void> _loadAllDevices() async {
    List<DeviceData>? devices = await LocationService.getAllDevices();
    if (devices != null) {
      if(mounted){
        setState(() {
          _allDevices = devices;
        });
      }
    }
  }

  Future<void> _getCurrentLocation() async {
    Position? position = await LocationService.getCurrentLocation();
    if (position != null) {
      if(mounted){
        setState(() {
          _currentPosition = position;
        });
      }
    }
  }

  void _toggleOnlineStatus() async {
    final service = FlutterBackgroundService();
    var isRunning = await service.isRunning();

    if (isRunning) {
      service.invoke("stopService");
      _positionStreamSubscription?.cancel();
      LocationService.stopPeriodicLocationUpdates();
      await LocationService.setOnlineStatus(false);
    } else {
      // Pedir permissões primeiro
      var notificationStatus = await Permission.notification.request();
      if (!notificationStatus.isGranted) {
        print("Permissão de notificação negada.");
        return;
      }
      
      await LocationService.requestPermissions();
      bool hasLocationPermission = await LocationService.checkPermissions();
      if (!hasLocationPermission) {
        print("Permissão de localização negada.");
        return;
      }

      await service.startService();
      await LocationService.setOnlineStatus(true);
      
      // Inicia o envio periódico de localização a cada 20 segundos
      await LocationService.startPeriodicLocationUpdates();
      
      // Mantém o stream de posição apenas para atualizar a UI
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10, // Atualiza a cada 10 metros
        )
      ).listen((Position position) {
        if(mounted){
          setState(() {
            _currentPosition = position;
          });
        }
      });
    }

    setState(() {
      _isOnline = !isRunning;
      _statusMessage = !isRunning ? 'Online' : 'Offline';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('GeoSec App'),
        backgroundColor: _isOnline ? Colors.green : Colors.red,
      ),
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              padding: EdgeInsets.only(
                left: 20,
                right: 20,
                top: 20,
                bottom: MediaQuery.of(context).viewInsets.bottom + 20,
              ),
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _isOnline ? Icons.radio_button_checked : Icons.radio_button_off,
                        size: 80,
                        color: _isOnline ? Colors.green : Colors.red,
                      ),
                      const SizedBox(height: 20),
                      Text(
                        'Status: $_statusMessage',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: _isOnline ? Colors.green : Colors.red,
                        ),
                      ),
                      const SizedBox(height: 40),
                      if (_deviceId.isNotEmpty) ...[
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              children: [
                                const Text(
                                  'ID do Dispositivo',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Text(
                                  _deviceId,
                                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        Card(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              children: [
                                const Text(
                                  'Apelido do Dispositivo',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 10),
                                Row(
                                  children: [
                                    Expanded(
                                      child: TextField(
                                        controller: _nicknameController,
                                        decoration: InputDecoration(
                                          hintText: 'Digite um apelido...',
                                          border: OutlineInputBorder(
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                        ),
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    ElevatedButton(
                                      onPressed: _saveNickname,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue,
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                      ),
                                      child: const Text('Salvar'),
                                    ),
                                  ],
                                ),
                                if (_nickname.isNotEmpty) ...[
                                  const SizedBox(height: 10),
                                  Text(
                                    'Apelido atual: $_nickname',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.green,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                      ],
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              if (_currentPosition != null) ...[
                                Card(
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Column(
                                      children: [
                                        const Text(
                                          'Coordenadas Atuais',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        Text(
                                          'Latitude: ${_currentPosition!.latitude.toStringAsFixed(6)}',
                                          style: const TextStyle(fontSize: 16),
                                        ),
                                        Text(
                                          'Longitude: ${_currentPosition!.longitude.toStringAsFixed(6)}',
                                          style: const TextStyle(fontSize: 16),
                                        ),
                                        Text(
                                          'Precisão: ${_currentPosition!.accuracy.toStringAsFixed(2)}m',
                                          style: const TextStyle(fontSize: 14, color: Colors.grey),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 20),
                              ],
                              if (_allDevices.isNotEmpty) ...[
                                Card(
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Column(
                                      children: [
                                        Text(
                                          'Dispositivos Conectados (${_allDevices.length})',
                                          style: const TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        SizedBox(
                                          height: 200,
                                          child: ListView.builder(
                                            itemCount: _allDevices.length,
                                            itemBuilder: (context, index) {
                                              DeviceData device = _allDevices[index];
                                              bool isCurrentDevice = device.deviceId == _deviceId;
                                              return Card(
                                                color: isCurrentDevice ? Colors.green.shade100 : null,
                                                child: ListTile(
                                                  leading: Icon(
                                                    isCurrentDevice ? Icons.smartphone : Icons.devices_other,
                                                    color: isCurrentDevice ? Colors.green : Colors.grey,
                                                  ),
                                                  title: Text(
                                                    isCurrentDevice ? 'Este Dispositivo' : 'Dispositivo',
                                                    style: TextStyle(
                                                      fontWeight: isCurrentDevice ? FontWeight.bold : FontWeight.normal,
                                                    ),
                                                  ),
                                                  subtitle: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      Text(
                                                        device.nickname != null && device.nickname!.isNotEmpty
                                                            ? '${device.nickname} (${device.deviceId.substring(0, 15)}...)'
                                                            : 'ID: ${device.deviceId.substring(0, 15)}...',
                                                        overflow: TextOverflow.ellipsis,
                                                        style: TextStyle(
                                                          fontWeight: device.nickname != null && device.nickname!.isNotEmpty
                                                              ? FontWeight.bold
                                                              : FontWeight.normal,
                                                        ),
                                                      ),
                                                      Text('Lat: ${device.latitude.toStringAsFixed(4)}, Lng: ${device.longitude.toStringAsFixed(4)}'),
                                                      Text('Atualizado: ${device.receivedAt.split(' ')[1]}'),
                                                    ],
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: _toggleOnlineStatus,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isOnline ? Colors.red : Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                        ),
                        child: Text(
                          _isOnline ? 'Ficar Offline' : 'Ficar Online',
                          style: const TextStyle(fontSize: 18),
                        ),
                      ),
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          ElevatedButton(
                            onPressed: _getCurrentLocation,
                            child: const Text('Atualizar Posição'),
                          ),
                          ElevatedButton(
                            onPressed: _loadAllDevices,
                            child: const Text('Atualizar Lista'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
} 