# GeoSec App

Este é um aplicativo Flutter que rastreia a localização GPS do usuário e a envia para um servidor.

## Funcionalidades

-   Obtém a localização GPS (latitude e longitude).
-   Permite que o usuário fique "Online" ou "Offline".
-   Quando "Online", envia as coordenadas a cada 30 segundos para um endpoint PHP.
-   Continua enviando a localização mesmo que o aplicativo esteja em segundo plano (a cada 15 minutos).
-   Envia um ID único do dispositivo para o servidor.
-   Busca e exibe a localização de todos os outros dispositivos conectados.
-   A interface mostra o status atual, as coordenadas e a lista de dispositivos.

## Como Funciona

### Aplicativo Flutter

-   **Tela Principal (`home_screen.dart`)**: Contém a interface do usuário com o botão de status, exibição de coordenadas e a lista de todos os dispositivos.
-   **Serviço de Localização (`location_service.dart`)**: Gerencia a obtenção das permissões, a busca pelas coordenadas GPS, a comunicação com o servidor e a obtenção do ID do dispositivo.
-   **`main.dart`**: Inicializa o aplicativo e o `workmanager` para as tarefas em segundo plano.
-   **`pubspec.yaml`**: Define as dependências do projeto, como `geolocator`, `http`, `workmanager` e `device_info_plus`.

### Backend (PHP)

-   **`geotest.php`**: Um script simples que:
    -   Recebe dados de localização via `POST` (com device_id, latitude, longitude).
    -   Salva os dados em um arquivo de texto (`geolocations.txt`), atualizando a entrada se o dispositivo já existir.
    -   Fornece uma lista de todos os dispositivos ativos quando recebe uma requisição `GET`.
    -   Remove automaticamente os registros de dispositivos que não enviam atualizações há mais de 5 minutos.
