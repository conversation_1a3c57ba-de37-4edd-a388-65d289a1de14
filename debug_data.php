<?php
header('Content-Type: text/html; charset=utf-8');
date_default_timezone_set('America/Sao_Paulo');

$dataFile = '../dados/geolocations.txt';

echo "<h1>Debug - Arquivo de Dados</h1>";
echo "<p>Arquivo: $dataFile</p>";

if (!file_exists($dataFile)) {
    echo "<p style='color: red;'>❌ Arquivo não existe!</p>";
    echo "<p>Verifique se a pasta '../dados/' existe e tem permissões de escrita.</p>";
    exit;
}

echo "<p style='color: green;'>✅ Arquivo existe</p>";

$fileSize = filesize($dataFile);
echo "<p>Tamanho do arquivo: " . number_format($fileSize) . " bytes</p>";

$lines = file($dataFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
echo "<p>Total de linhas: " . count($lines) . "</p>";

if (count($lines) == 0) {
    echo "<p style='color: orange;'>⚠️ Arquivo vazio - nenhum dispositivo registrado</p>";
    exit;
}

echo "<h2>Conteúdo do Arquivo:</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #f0f0f0;'>";
echo "<th>Linha</th>";
echo "<th>Device ID</th>";
echo "<th>Apelido</th>";
echo "<th>Latitude</th>";
echo "<th>Longitude</th>";
echo "<th>Timestamp</th>";
echo "<th>Recebido em</th>";
echo "<th>Status</th>";
echo "</tr>";

$now = time();
$validDevices = 0;
$expiredDevices = 0;

foreach ($lines as $index => $line) {
    $data = json_decode($line, true);
    
    if ($data) {
        $isExpired = false;
        if (isset($data['received_at_unix'])) {
            $age = $now - $data['received_at_unix'];
            $isExpired = $age > 60;
        }
        
        if ($isExpired) {
            $expiredDevices++;
            $status = "<span style='color: red;'>EXPIRADO (" . $age . "s)</span>";
        } else {
            $validDevices++;
            $status = "<span style='color: green;'>ATIVO</span>";
        }
        
        echo "<tr>";
        echo "<td>" . ($index + 1) . "</td>";
        echo "<td>" . htmlspecialchars($data['device_id'] ?? 'N/A') . "</td>";
        echo "<td>" . htmlspecialchars($data['nickname'] ?? 'N/A') . "</td>";
        echo "<td>" . ($data['latitude'] ?? 'N/A') . "</td>";
        echo "<td>" . ($data['longitude'] ?? 'N/A') . "</td>";
        echo "<td>" . ($data['timestamp'] ?? 'N/A') . "</td>";
        echo "<td>" . ($data['received_at'] ?? 'N/A') . "</td>";
        echo "<td>$status</td>";
        echo "</tr>";
    } else {
        echo "<tr style='background-color: #ffe6e6;'>";
        echo "<td>" . ($index + 1) . "</td>";
        echo "<td colspan='6' style='color: red;'>❌ Erro ao decodificar JSON: " . htmlspecialchars($line) . "</td>";
        echo "</tr>";
    }
}

echo "</table>";

echo "<h2>Resumo:</h2>";
echo "<ul>";
echo "<li>Dispositivos válidos: <strong>$validDevices</strong></li>";
echo "<li>Dispositivos expirados: <strong>$expiredDevices</strong></li>";
echo "<li>Total de linhas: <strong>" . count($lines) . "</strong></li>";
echo "</ul>";

echo "<h2>Teste de API:</h2>";
echo "<p><a href='geotest.php' target='_blank'>Clique aqui para testar a API GET</a></p>";

echo "<h2>Logs do Servidor:</h2>";
$logFile = ini_get('error_log');
if ($logFile && file_exists($logFile)) {
    $logs = file($logFile);
    $recentLogs = array_slice($logs, -20); // Últimas 20 linhas
    echo "<pre style='background-color: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: auto;'>";
    foreach ($recentLogs as $log) {
        echo htmlspecialchars($log);
    }
    echo "</pre>";
} else {
    echo "<p>Logs não disponíveis</p>";
}
?> 