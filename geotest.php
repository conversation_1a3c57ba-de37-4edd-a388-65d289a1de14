<?php
header('Content-Type: application/json');
date_default_timezone_set('America/Sao_Paulo');

$dataFile = '../dados/geolocations.txt';

// Função para limpar dados antigos (mais de 1 minuto)
function cleanOldData($dataFile) {
    if (!file_exists($dataFile)) {
        error_log("Arquivo de dados não existe: $dataFile");
        return;
    }
    $lines = file($dataFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $freshData = [];
    $now = time();
    $removedCount = 0;

    foreach ($lines as $line) {
        $data = json_decode($line, true);
        if ($data && (isset($data['received_at_unix']))) {
            if (($now - $data['received_at_unix']) <= 60) { // 60 segundos = 1 minuto
                $freshData[] = $line;
            } else {
                $removedCount++;
            }
        }
    }
    
    if ($removedCount > 0) {
        error_log("Removidos $removedCount dispositivos antigos");
    }
    
    file_put_contents($dataFile, implode(PHP_EOL, $freshData) . PHP_EOL);
    error_log("Arquivo atualizado com " . count($freshData) . " dispositivos ativos");
}

// Limpa dados antigos antes de qualquer operação
cleanOldData($dataFile);

// Se a requisição for POST, salva os dados
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    error_log("POST recebido: " . json_encode($input));

    if (isset($input['device_id']) && isset($input['latitude']) && isset($input['longitude']) && isset($input['timestamp'])) {
        
        $newData = [
            'device_id' => $input['device_id'],
            'latitude' => $input['latitude'],
            'longitude' => $input['longitude'],
            'timestamp' => $input['timestamp'],
            'received_at' => date('Y-m-d H:i:s'),
            'received_at_unix' => time()
        ];

        // Adiciona nickname se fornecido
        if (isset($input['nickname']) && !empty($input['nickname'])) {
            $newData['nickname'] = $input['nickname'];
        }

        $lines = file_exists($dataFile) ? file($dataFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES) : [];
        $updated = false;
        $newLines = [];
        $nicknameKey = isset($newData['nickname']) ? $newData['nickname'] : null;
        
        foreach ($lines as $line) {
            $existingData = json_decode($line, true);
            if ($existingData && isset($existingData['nickname']) && $nicknameKey && $existingData['nickname'] === $nicknameKey) {
                // Verifica se algum dado mudou
                if (
                    $existingData['latitude'] != $newData['latitude'] ||
                    $existingData['longitude'] != $newData['longitude'] ||
                    $existingData['timestamp'] != $newData['timestamp'] ||
                    $existingData['device_id'] != $newData['device_id']
                ) {
                    $newLines[] = json_encode($newData);
                    $updated = true;
                    error_log("Dispositivo (nickname) atualizado: " . $nicknameKey);
                } else {
                    $newLines[] = $line; // Mantém a linha original
                }
            } else {
                $newLines[] = $line;
            }
        }
        
        if (!$updated && $nicknameKey) {
            // Se não encontrou, adiciona novo
            $newLines[] = json_encode($newData);
            error_log("Novo dispositivo adicionado (nickname): " . $nicknameKey);
        }

        file_put_contents($dataFile, implode(PHP_EOL, $newLines) . PHP_EOL);

        // Após salvar, lê todos os dados atualizados para retornar
        $allDevices = [];
        $updatedLines = file($dataFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach($updatedLines as $line) {
             $allDevices[] = json_decode($line, true);
        }

        error_log("Total de dispositivos retornados: " . count($allDevices));
        echo json_encode(['success' => true, 'message' => 'Localização salva.', 'devices' => $allDevices]);

    } else {
        error_log("Dados incompletos recebidos: " . json_encode($input));
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Dados incompletos.']);
    }
    exit;
}

// Se a requisição for PUT, atualiza apenas o nickname
if ($_SERVER['REQUEST_METHOD'] === 'PUT') {
    $input = json_decode(file_get_contents('php://input'), true);
    error_log("PUT recebido: " . json_encode($input));

    if (isset($input['device_id']) && isset($input['nickname'])) {
        $lines = file_exists($dataFile) ? file($dataFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES) : [];
        $deviceFound = false;
        
        foreach ($lines as $key => $line) {
            $existingData = json_decode($line, true);
            if ($existingData && $existingData['device_id'] === $input['device_id']) {
                $existingData['nickname'] = $input['nickname'];
                $lines[$key] = json_encode($existingData);
                $deviceFound = true;
                error_log("Nickname atualizado para: " . $input['device_id'] . " -> " . $input['nickname']);
                break;
            }
        }
        
        if ($deviceFound) {
            file_put_contents($dataFile, implode(PHP_EOL, $lines) . PHP_EOL);
            echo json_encode(['success' => true, 'message' => 'Nickname atualizado.']);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Dispositivo não encontrado.']);
        }
    } else {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Dados incompletos.']);
    }
    exit;
}

// Se a requisição for GET, retorna todos os dados
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $allDevices = [];
    if (file_exists($dataFile)) {
        $lines = file($dataFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        error_log("GET: Lendo " . count($lines) . " linhas do arquivo");
        foreach($lines as $line) {
             $deviceData = json_decode($line, true);
             if ($deviceData) {
                 $allDevices[] = $deviceData;
             } else {
                 error_log("Erro ao decodificar linha: $line");
             }
        }
    } else {
        error_log("Arquivo de dados não existe para GET: $dataFile");
    }
    
    error_log("GET: Retornando " . count($allDevices) . " dispositivos");
    echo json_encode(['success' => true, 'devices' => $allDevices]);
    exit;
}

// Se não for POST, PUT ou GET
http_response_code(405);
echo json_encode(['success' => false, 'message' => 'Método não permitido.']);

?> 