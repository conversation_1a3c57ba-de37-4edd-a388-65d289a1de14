package com.example.geo_tracker_app

import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine

class MainActivity : FlutterActivity() {
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        // Cria o canal de notificação antes de o Flutter inicializar
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channelId = "geosec_service_channel"
            val channel = NotificationChannel(
                channelId,
                "GeoSec Serviço",
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "Canal do serviço em primeiro plano da GeoSec"
            }
            val nm = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            nm.createNotificationChannel(channel)
        }
        super.configureFlutterEngine(flutterEngine)
    }
}
